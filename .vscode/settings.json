{
  // ✅ Use Biome for formatting
  "editor.defaultFormatter": "biomejs.biome",

  // ✅ Format on save
  "editor.formatOnSave": true,

  // ✅ Enable Biome extension linting
  "biome.enableLinter": true,

  // ✅ Disable ESLint if you have it installed (optional)
  "eslint.enable": false,

  // ✅ Format on paste or type (optional)
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,

  // ✅ Use 2 spaces indentation
  "editor.tabSize": 2,

  // ✅ Recommended for TypeScript/React projects
  "files.eol": "\n"
}
