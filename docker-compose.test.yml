version: '3.8'

services:
  postgres-test:
    image: postgres:15
    restart: always
    container_name: lb2_db_test
    ports:
      - '5433:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: lb2_db_test
    volumes:
      - postgres_test_data:/var/lib/postgresql/data

  mailhog-test:
    image: mailhog/mailhog
    restart: always
    container_name: mailhog_test
    ports:
      - '8026:8025'  # Web UI
      - '1026:1025'  # SMTP

volumes:
  postgres_test_data:
