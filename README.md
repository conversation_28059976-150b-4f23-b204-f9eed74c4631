# Lovebook v2

A Next.js application for creating personalized love story books with OTP authentication.

## Features

- 🔐 **OTP Authentication**: Secure email-based login system
- 💌 **Email Integration**: MailHog for development email testing
- 🗄️ **Database**: PostgreSQL with Prisma ORM
- 🎨 **UI**: HeroUI components with Tailwind CSS
- 🧪 **E2E Testing**: Playwright + Cucumber BDD framework
- 🌍 **Internationalization**: Russian language support

## Getting Started

### Development

```bash
# Install dependencies
npm install

# Start development services (PostgreSQL + MailHog)
docker-compose up -d

# Run database migrations
npm run prisma:migrate:dev

# Start development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Testing

```bash
# Setup test environment
npm run test:setup

# Run E2E tests
npm run test:e2e

# Run Cucumber BDD tests
npm run test:cucumber

# Cleanup test environment
npm run test:teardown
```

See [tests/README.md](tests/README.md) for detailed testing documentation.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
