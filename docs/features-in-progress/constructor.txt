Book Constructor — Detailed Feature Description
🎯 Goal
Build a super intuitive, easy-to-use book constructor for all users, including elderly people, where users can:

Create a new book on a dedicated page (no modals)

Add pages with predefined layouts (text, images, mixed)

Add & edit text blocks with simple styling: font type, size, bold, italic, underline

Upload images easily using drag & drop (react-dropzone)

Reorder pages by drag & drop

Choose book variant (album or normal), controlling page aspect ratio

Changes are autosaved live to backend with debounce and server actions

Unfinished books stay in a Drafts list, so work is never lost

Mark books as finished when done, removing from drafts

Output the final book for printing/typography

The interface must be minimal, clear, and friendly to non-technical and elderly users — large buttons, simple workflows, clear save status, and no surprises.

🧰 Tech Stack
Framework: Next.js (App Router with app/ directory)

UI Library: HeroUI (https://www.heroui.com/)

Styling: Tailwind CSS

File Upload: react-dropzone

Forms: react-hook-form + zod

Drag & Drop: @hello-pangea/dnd or dnd-kit

Icons: Lucide React

Autosave: Next.js Server Actions + React useTransition + debounce (lodash.debounce)

Database: Prisma or any backend DB

PDF Export: react-pdf or fallback html2canvas + jsPDF

🏗️ Project Structure (App Router)
bash
Copy
Edit
/app
 ├─ /new
 │    └─ page.tsx           # New book creation page
 ├─ /drafts
 │    └─ page.tsx           # List of draft books
 ├─ /book
 │    └─ /[id]
 │         └─ page.tsx      # Book editor page
/components
 ├─ BookEditor.tsx          # Main editor shell (client)
 ├─ PageList.tsx            # Pages sidebar with drag & drop
 ├─ LayoutPicker.tsx        # Layout selector modal
 ├─ Block.tsx               # Text/image block component
 ├─ TextToolbar.tsx         # Text styling toolbar
 ├─ ImageDropzone.tsx       # React-dropzone wrapper for image upload
/lib
 ├─ layouts.ts              # Predefined layout templates
 ├─ debounceSave.ts         # Custom debounce hook
/actions
 ├─ books.ts                # Server Actions for book CRUD
📝 Data Models (Typescript)
ts
Copy
Edit
interface Book {
  id: string;
  title: string;
  variant: 'album' | 'normal';
  status: 'draft' | 'finished';
  pages: Page[];
  userId: string;
}

interface Page {
  id: string;
  layoutId: string;          // Refers to a layout template
  blocks: Block[];
}

interface Block {
  id: string;
  type: 'text' | 'image';
  content: string;           // Text content or image URL
  styles?: {
    fontFamily?: string;
    fontSize?: number;
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
  };
}
🔑 Core Features
1. New Book Creation Page (/new)
Simple form with:

Title input (required)

Variant radio (album/normal)

On submit, calls Server Action createBook()

Redirects to /book/[id]

2. Drafts Page (/drafts)
Lists all unfinished (status: 'draft') books by current user

Each draft clickable to open editor

3. Book Editor Page (/book/[id])
Displays the book title & variant

Shows pages in a reorderable list/sidebar (PageList)

Ability to add a page → opens LayoutPicker modal with layout thumbnails

Each page rendered with blocks (text and image)

Text blocks are editable with a floating TextToolbar:

Font family, font size, bold, italic, underline

Image blocks support drag & drop upload (react-dropzone)

All changes auto-save via Server Actions + debounce + useTransition

Show save status (e.g. “Saving...” or ✔️)

Button to Mark as Finished

4. Layouts
Provide predefined layouts for page blocks:

Text only

Image only

Text left / Image right

Image left / Text right

Two images + text

Four image grid

5. Autosave
On any change (text, image, page order), update local state and call Server Action debounced (e.g., 500ms delay)

Use React useTransition to mark saving state

Show feedback in UI

6. Page Reordering
Use drag & drop library to reorder pages

Persist order via autosave

7. Image Upload
Each image block uses react-dropzone

Uploads image and saves URL in block content

UI preview shown

Allow replacing/removing image

8. Mark as Finished
Button triggers Server Action to update book status

Book removed from Drafts list

9. Export for Print
Later: Implement PDF export using react-pdf or html2canvas + jsPDF

🔧 Implementation Advice
Use Next.js Server Actions for all DB writes (createBook, updateBook, finishBook)

Use React’s useTransition for smooth autosave UX

Use lodash.debounce to reduce network calls on frequent changes

Use HeroUI components to build large, accessible UI elements

Use contentEditable divs for simple text editing, combined with a floating toolbar

Store uploaded images on your preferred storage (e.g., S3) and save URLs in DB

Keep data normalized — e.g., pages are array inside book, blocks array inside page

Provide clear feedback for saving status

Prioritize minimal clicks and simple interface for elderly users

⚙️ Example Packages To Install
bash
Copy
Edit
npm install react-hook-form @hookform/resolvers zod react-dropzone @hello-pangea/dnd lucide-react lodash.debounce
🔥 Next Steps?
