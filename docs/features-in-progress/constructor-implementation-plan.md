# Book Constructor - Implementation Plan

## Updated Implementation Plan

### **Phase 1: UI Mockups & Design System** 🎨
- [ ] Create responsive constructor layout mockups
- [ ] Design page editor with canvas-like editing area that respects book dimensions
- [ ] Design drag & drop block system (text/image blocks)
- [ ] Create layout templates with flexible block positioning
- [ ] Design progress indicators for book completion
- [ ] Mobile-responsive design considerations

### **Phase 2: Database Schema & Core Models** 🗄️
- [ ] Extend Prisma schema with Book, Page, Block models
- [ ] Add support for flexible block positioning (x, y, width, height)
- [ ] Create database migrations
- [ ] Set up TypeScript interfaces

### **Phase 3: Abstract Image Storage** 📁
- [ ] Create abstract ImageStorage interface
- [ ] Implement LocalFileStorage class (stores in project files)
- [ ] Set up image upload endpoints with web-standard formats (JPEG, PNG, WebP)
- [ ] Implement reasonable size limits (e.g., 10MB max)

### **Phase 4: Core Components** ⚛️
- [ ] BookEditor with canvas-like editing area
- [ ] Flexible block system (draggable text/image blocks)
- [ ] Research and potentially integrate react-grid-layout for block positioning
- [ ] Layout picker with predefined templates + manual block addition
- [ ] Text formatting toolbar
- [ ] Image upload with react-dropzone

### **Phase 5: Pages & Routing** 🛣️
- [ ] `/constructor/new` - New book creation
- [ ] `/constructor/drafts` - Drafts listing with progress indicators
- [ ] `/constructor/book/[id]` - Book editor with canvas
- [ ] Update dashboard with constructor navigation

### **Phase 6: Advanced Interactions** 🎯
- [ ] Drag & drop page reordering
- [ ] Block drag & drop within pages
- [ ] Autosave with visual feedback
- [ ] Mark as finished with confirmation dialog
- [ ] Preview mode before finishing

### **Phase 7: Polish & UX** ✨
- [ ] Loading states and error handling
- [ ] Canvas responsiveness (editor responsive, canvas maintains aspect ratio)
- [ ] Progress completion indicators
- [ ] User testing and refinements

---

## **Future/Low Priority Features** 🔮
*(We'll implement these later)*
- [ ] Onboarding tutorial
- [ ] Keyboard shortcuts (Ctrl+S, Ctrl+Z)
- [ ] Auto-backup system
- [ ] Image compression/optimization
- [ ] Collaborative features
- [ ] Custom layout creation tools

---

## **Key Technical Decisions:**

### **Canvas-Style Editor Approach:**
Similar to Canva, we'll create:
- **Responsive editor interface** (toolbar, sidebar, etc.)
- **Fixed-aspect-ratio canvas** that represents the actual book page
- **Absolute positioning** for blocks within the canvas
- **Zoom controls** for detailed editing

### **Flexible Block System:**
- Start with predefined layouts as templates
- Allow users to add individual text/image blocks
- Use drag & drop for positioning (react-grid-layout or custom solution)
- Store block positions as coordinates relative to page dimensions

### **Dependencies to Install:**
```bash
npm install react-hook-form @hookform/resolvers zod react-dropzone @hello-pangea/dnd lucide-react lodash.debounce @types/lodash.debounce react-grid-layout
```

### **Updated File Structure:**
```
src/
├── app/
│   ├── constructor/
│   │   ├── new/page.tsx
│   │   ├── drafts/page.tsx
│   │   └── book/[id]/page.tsx
├── components/
│   ├── constructor/
│   │   ├── BookEditor.tsx          # Main canvas editor
│   │   ├── PageCanvas.tsx          # Fixed-ratio editing canvas
│   │   ├── BlockSystem/            # Draggable blocks
│   │   │   ├── TextBlock.tsx
│   │   │   ├── ImageBlock.tsx
│   │   │   └── BlockWrapper.tsx
│   │   ├── PageList.tsx            # Page sidebar
│   │   ├── LayoutPicker.tsx        # Template + manual options
│   │   ├── TextToolbar.tsx
│   │   ├── ImageDropzone.tsx
│   │   └── ProgressIndicator.tsx
├── lib/
│   ├── layouts.ts                  # Predefined templates
│   ├── debounce-save.ts
│   └── storage/                    # Abstract storage
│       ├── ImageStorage.ts         # Interface
│       └── LocalFileStorage.ts     # Implementation
└── actions/
    └── books.ts
```

## **User Experience Decisions:**
- **Constructor accessible from dashboard**: Yes, "Create New Book" button leads to constructor
- **Preview mode**: Yes, users can preview before marking as finished
- **Confirmation dialog**: Yes, when marking book as finished
- **Image storage**: Local filesystem with abstract class for future S3 migration
- **Image formats**: Web standards (JPEG, PNG, WebP)
- **Responsive layouts**: Editor responsive, canvas maintains book dimensions
- **Manual block positioning**: Yes, using drag & drop with react-grid-layout
- **Progress indicators**: Yes, show completion percentage

## **Implementation Priority:**
1. **High Priority**: Core functionality (Phases 1-7)
2. **Low Priority**: Polish features (tutorials, shortcuts, auto-backup)
3. **Future**: Advanced features (collaboration, custom layouts)
