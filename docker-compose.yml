version: '3.8'

services:
  postgres:
    image: postgres:15
    restart: always
    container_name: lb2_db
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: lb2_db
    volumes:
      - postgres_data:/var/lib/postgresql/data

  mailhog:
    image: mailhog/mailhog
    restart: always
    container_name: mailhog
    ports:
      - '8025:8025'  # Web UI
      - '1025:1025'  # SMTP

volumes:
  postgres_data:
