{"name": "lovebook-v2", "version": "0.1.0", "private": true, "scripts": {"prepare": "lefthook install", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome check .", "format": "biome format . --write", "typecheck": "tsc --noEmit", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset --force", "db:seed": "ts-node prisma/seed.ts", "test:setup": "pkill -f 'next.*3001' || true && docker-compose -f docker-compose.test.yml up -d && sleep 5 && npm run prisma:generate && dotenv -e .env.test -- npx prisma migrate deploy && npm run test:start-server && sleep 10", "test:start-server": "dotenv -e .env.test -- next dev --port 3001 &", "test:teardown": "docker-compose -f docker-compose.test.yml down -v && pkill -f 'next.*3001' || true", "test:e2e": "npm run test:setup && npx playwright test && npm run test:teardown", "test:cucumber": "npx cucumber-js --config tests/e2e/config/cucumber.config.js tests/e2e/features", "test": "pkill -f 'next.*3001' || true && npm run test:setup && npm run test:cucumber && npm run test:teardown"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@heroui/react": "^2.7.11", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@types/lodash.debounce": "^4.0.9", "@types/react-grid-layout": "^1.3.5", "bcrypt": "^6.0.0", "framer-motion": "^12.23.0", "jsonwebtoken": "^9.0.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.525.0", "next": "15.3.4", "nodemailer": "^7.0.5", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.60.0", "zod": "^4.0.4"}, "devDependencies": {"@arkweid/lefthook": "^0.7.7", "@biomejs/biome": "2.1.1", "@cucumber/cucumber": "^11.3.0", "@cucumber/pretty-formatter": "^1.0.1", "@eslint/eslintrc": "^3", "@playwright/test": "^1.53.2", "@tailwindcss/typography": "^0.5.16", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "dotenv-cli": "^8.0.0", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}