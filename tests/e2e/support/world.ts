import {
  type IWorldOptions,
  setWorldConstructor,
  World,
} from "@cucumber/cucumber";
import {
  type Browser,
  type BrowserContext,
  chromium,
  type Page,
} from "@playwright/test";
import { DashboardPage } from "./page-objects/dashboard-page";
import { LoginPage } from "./page-objects/login-page";

export interface ICustomWorld extends World {
  browser?: Browser;
  context?: BrowserContext;
  page?: Page;
  loginPage?: LoginPage;
  dashboardPage?: DashboardPage;
  baseUrl: string;
  testEmail?: string;
  otpCode?: string;
  init(): Promise<void>;
  cleanup(): Promise<void>;
  pickle?: {
    name?: string;
  };
}

export class CustomWorld extends World implements ICustomWorld {
  browser?: Browser;
  context?: BrowserContext;
  page?: Page;
  loginPage?: LoginPage;
  dashboardPage?: DashboardPage;
  baseUrl: string;
  testEmail?: string;
  otpCode?: string;

  constructor(options: IWorldOptions) {
    super(options);
    this.baseUrl =
      options.parameters?.baseUrl ||
      process.env.NEXT_PUBLIC_APP_URL ||
      "http://localhost:3001";
  }

  async init() {
    this.browser = await chromium.launch({
      headless: process.env.CI === "true",
      slowMo: process.env.CI === "true" ? 0 : 100,
    });
    this.context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 },
      ignoreHTTPSErrors: true,
    });
    this.page = await this.context.newPage();

    // Initialize page objects
    this.loginPage = new LoginPage(this.page, this.baseUrl);
    this.dashboardPage = new DashboardPage(this.page, this.baseUrl);
  }

  async cleanup() {
    if (this.context) {
      await this.context.close();
    }
    if (this.browser) {
      await this.browser.close();
    }
  }
}

setWorldConstructor(CustomWorld);
