export interface EmailMessage {
  ID: string;
  From: { Mailbox: string; Domain: string };
  To: { Mailbox: string; Domain: string }[];
  Content: {
    Headers: { Subject: string[] };
    Body: string;
  };
  Created: string;
}

export class MailHelper {
  private baseUrl: string;

  constructor() {
    this.baseUrl = "http://localhost:8026"; // MailHog test instance
  }

  async getEmails(): Promise<EmailMessage[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v2/messages`);
      if (!response.ok) {
        throw new Error(`Failed to fetch emails: ${response.status}`);
      }
      const data = await response.json();
      return data.items || [];
    } catch (error) {
      console.error("Error fetching emails:", error);
      return [];
    }
  }

  async getEmailsForRecipient(email: string): Promise<EmailMessage[]> {
    const emails = await this.getEmails();
    return emails.filter((msg) =>
      msg.To?.some?.(
        (recipient) => `${recipient.Mailbox}@${recipient.Domain}` === email,
      ),
    );
  }

  async getLatestEmailForRecipient(
    email: string,
  ): Promise<EmailMessage | null> {
    const emails = await this.getEmailsForRecipient(email);
    return emails.length > 0 ? emails[0] : null;
  }

  async extractOtpFromEmail(email: EmailMessage): Promise<string | null> {
    // Extract 6-digit OTP code from email body
    const otpMatch = email.Content.Body.match(/\b\d{6}\b/);
    return otpMatch ? otpMatch[0] : null;
  }

  async getOtpForEmail(recipientEmail: string): Promise<string | null> {
    const email = await this.getLatestEmailForRecipient(recipientEmail);
    if (!email) {
      return null;
    }
    return await this.extractOtpFromEmail(email);
  }

  async clearEmails(): Promise<void> {
    try {
      await fetch(`${this.baseUrl}/api/v1/messages`, {
        method: "DELETE",
      });
    } catch (error) {
      console.error("Error clearing emails:", error);
    }
  }

  async waitForEmail(
    recipientEmail: string,
    timeoutMs: number = 10000,
  ): Promise<EmailMessage | null> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
      const email = await this.getLatestEmailForRecipient(recipientEmail);
      if (email) {
        return email;
      }
      // Wait 500ms before checking again
      await new Promise((resolve) => setTimeout(resolve, 500));
    }

    return null;
  }

  async waitForOtp(
    recipientEmail: string,
    timeoutMs: number = 10000,
  ): Promise<string | null> {
    const email = await this.waitForEmail(recipientEmail, timeoutMs);
    if (!email) {
      return null;
    }
    return await this.extractOtpFromEmail(email);
  }
}
