{"extends": "../tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": ".", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node", "@types/node", "@playwright/test", "@cucumber/cucumber"]}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"], "ts-node": {"compilerOptions": {"module": "CommonJS", "target": "ES2022"}}}