# E2E Testing with <PERSON>wright & Cucumber

This directory contains end-to-end tests using <PERSON>wright and Cucumber BDD framework.

## Setup

### Prerequisites

- <PERSON>er and Docker Compose
- Node.js and npm

### Test Environment

The test environment uses:
- **PostgreSQL**: Separate test database on port 5433
- **MailHog**: Email testing service on ports 8026 (UI) and 1026 (SMTP)
- **Next.js**: Test server on port 3001

## Running Tests

### Quick Start

```bash
# Setup test environment
npm run test:setup

# Run all E2E tests
npm run test:e2e

# Run Cucumber BDD tests
npm run test:cucumber

# Teardown test environment
npm run test:teardown
```

### Development Mode

```bash
# Setup test environment (one time)
npm run test:setup

# Run Cucumber tests in development mode (no teardown)
npm run test:cucumber:dev

# Run Playwright tests with UI
npm run test:e2e:ui

# Teardown when done
npm run test:teardown
```

## Test Structure

```
tests/
├── e2e/
│   ├── features/           # Cucumber feature files (.feature)
│   │   └── auth.feature   # Authentication scenarios
│   ├── step-definitions/   # Step implementations
│   │   └── auth.steps.ts  # Authentication steps
│   ├── support/           # Test utilities
│   │   ├── world.ts       # Cucumber World setup
│   │   ├── hooks.ts       # Before/After hooks
│   │   ├── database-helper.ts  # Database utilities
│   │   ├── mail-helper.ts      # Email testing utilities
│   │   └── page-objects/       # Page Object Model
│   │       ├── login-page.ts
│   │       └── dashboard-page.ts
│   └── config/
│       └── cucumber.config.ts  # Cucumber configuration
├── setup-test-env.ts      # Environment setup script
└── README.md              # This file
```

## Environment Variables

Test environment variables are configured in `.env.test`:

```env
DATABASE_URL=postgresql://postgres:postgres@localhost:5433/lb2_db_test
EMAIL_HOST=localhost
EMAIL_PORT=1026
JWT_SECRET=test_super_secret_key_for_testing_only
JWT_EXPIRATION_DAYS=1
OTP_EXPIRATION_MINUTES=1
NODE_ENV=test
NEXT_PUBLIC_APP_URL=http://localhost:3001
```

## Features Tested

### Authentication Flow
- ✅ Successful OTP login
- ✅ Invalid email format handling
- ✅ Expired OTP handling
- ✅ Incorrect OTP handling
- ✅ Multiple OTP requests
- ✅ User logout
- ✅ Protected route access
- ✅ Session persistence
- ✅ OTP expiration configuration

## Page Objects

The tests use the Page Object Model pattern for better maintainability:

- **LoginPage**: Handles login form interactions
- **DashboardPage**: Handles dashboard interactions

## Utilities

- **DatabaseHelper**: Database operations and cleanup
- **MailHelper**: Email verification and OTP extraction
- **World**: Cucumber world setup with browser context

## Debugging

### Screenshots
Failed tests automatically capture screenshots in `test-results/screenshots/`

### Videos
Failed tests record videos in `test-results/`

### MailHog UI
Access the MailHog web interface at http://localhost:8026 to inspect emails during test development.

### Database Access
Connect to the test database:
```bash
psql -h localhost -p 5433 -U postgres -d lb2_db_test
```

## CI/CD Integration

Tests are configured to run in CI environments with:
- Headless browser mode
- Retry on failure
- Parallel execution disabled for stability
- Comprehensive reporting (HTML, JSON, JUnit)

## Troubleshooting

### Port Conflicts
If you get port conflicts, ensure no other services are running on:
- 3001 (Next.js test server)
- 5433 (PostgreSQL test)
- 8026/1026 (MailHog test)

### Database Issues
Reset the test database:
```bash
npm run test:teardown
npm run test:setup
```

### Email Issues
Clear MailHog emails:
```bash
curl -X DELETE http://localhost:8026/api/v1/messages
```
