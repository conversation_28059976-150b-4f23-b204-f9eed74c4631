import { NextResponse } from "next/server";
import { signJwt } from "@/lib/jwt";
import { prisma } from "@/lib/prisma";

export async function POST(req: Request) {
  try {
    const { email, code } = await req.json();

    if (typeof email !== "string" || typeof code !== "string") {
      return NextResponse.json({ error: "Invalid input" }, { status: 400 });
    }

    const otp = await prisma.otp.findFirst({
      where: {
        email,
        code,
        expiresAt: { gt: new Date() },
      },
    });

    if (!otp) {
      return NextResponse.json(
        { error: "Invalid or expired OTP" },
        { status: 400 },
      );
    }

    let user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      user = await prisma.user.create({ data: { email } });
    }

    const token = signJwt({ id: user.id, email: user.email });

    const res = NextResponse.json({ message: "Authenticated" });
    res.cookies.set({
      name: "token",
      value: token,
      httpOnly: true,
      path: "/",
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
    });

    await prisma.otp.delete({ where: { id: otp.id } });

    return res;
  } catch (error) {
    console.error("Error in OTP login:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
