import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { DashboardContent } from "@/components/dashboard/dashboard-content";
import { HeroUIClientProvider } from "@/components/providers/heroui-provider";
import { verifyJwt } from "@/lib/jwt";

export default async function DashboardPage() {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!token) {
    redirect("/login");
  }

  const decoded = verifyJwt(token) as { id: string; email: string };

  return (
    <HeroUIClientProvider>
      <DashboardContent userEmail={decoded.email} />
    </HeroUIClientProvider>
  );
}
