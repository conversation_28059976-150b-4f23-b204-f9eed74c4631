"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardBody,
  Card<PERSON><PERSON><PERSON>,
  <PERSON>,
  Divider,
  Input,
  InputOtp,
} from "@heroui/react";
import { useState } from "react";
import { HeroUIClientProvider } from "@/components/providers/heroui-provider";

function LoginForm() {
  const [email, setEmail] = useState("");
  const [code, setCode] = useState("");
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState<"success" | "error" | "info">(
    "info",
  );
  const [isLoadingSendOtp, setIsLoadingSendOtp] = useState(false);
  const [isLoadingVerifyOtp, setIsLoadingVerifyOtp] = useState(false);
  const [otpSent, setOtpSent] = useState(false);

  async function handleSendOtp(e: React.FormEvent) {
    e.preventDefault();
    setIsLoadingSendOtp(true);
    setMessage("Отправка кода...");
    setMessageType("info");

    try {
      const res = await fetch("/api/auth/request-otp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      if (res.ok) {
        setMessage("Код отправлен! Проверьте вашу почту.");
        setMessageType("success");
        setOtpSent(true);
      } else {
        setMessage("Не удалось отправить код.");
        setMessageType("error");
      }
    } catch {
      setMessage("Ошибка сети.");
      setMessageType("error");
    } finally {
      setIsLoadingSendOtp(false);
    }
  }

  async function handleVerifyOtp(e: React.FormEvent) {
    e.preventDefault();
    setIsLoadingVerifyOtp(true);
    setMessage("Проверка кода...");
    setMessageType("info");

    try {
      const res = await fetch("/api/auth/verify-otp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, code }),
      });
      const data = await res.json();
      if (res.ok) {
        setMessage("Вход выполнен успешно!");
        setMessageType("success");
        // Redirect or update UI accordingly
        setTimeout(() => {
          window.location.href = "/dashboard";
        }, 1000);
      } else {
        setMessage(data.error || "Неверный код");
        setMessageType("error");
      }
    } catch {
      setMessage("Ошибка сети.");
      setMessageType("error");
    } finally {
      setIsLoadingVerifyOtp(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pink-50 to-purple-50 p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="flex flex-col gap-3 pb-6">
          <div className="flex flex-col items-center">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
              💕 Lovebook
            </h1>
            <p className="text-small text-default-500 mt-2">
              Войдите, чтобы создать свою историю любви
            </p>
          </div>
        </CardHeader>
        <Divider />
        <CardBody className="gap-6">
          {/* Send OTP Form */}
          {!otpSent ? (
            <form onSubmit={handleSendOtp} className="flex flex-col gap-4">
              <Input
                type="email"
                label="Адрес электронной почты"
                placeholder="Введите ваш email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                isRequired
                variant="bordered"
                color="secondary"
                startContent={
                  <div className="pointer-events-none flex items-center">
                    <span className="text-default-400 text-small">📧</span>
                  </div>
                }
                classNames={{
                  input: "text-small",
                  inputWrapper:
                    "border-1 border-default-200 hover:border-secondary-300 group-data-[focus=true]:border-secondary-500",
                }}
              />
              <Button
                type="submit"
                color="secondary"
                variant="solid"
                size="lg"
                isLoading={isLoadingSendOtp}
                className="bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold"
                startContent={!isLoadingSendOtp && <span>✨</span>}
              >
                {isLoadingSendOtp ? "Отправка..." : "Отправить магический код"}
              </Button>
            </form>
          ) : (
            <div className="text-center py-4">
              <div className="text-6xl mb-2">📬</div>
              <p className="text-small text-default-500">
                Проверьте вашу почту для получения кода подтверждения
              </p>
            </div>
          )}

          {/* Verify OTP Form */}
          {otpSent && (
            <>
              <Divider />
              <form onSubmit={handleVerifyOtp} className="flex flex-col gap-4">
                <div className="flex flex-col gap-3">
                  <div className="text-center">
                    <h3 className="text-large font-semibold text-foreground">
                      Введите код подтверждения
                    </h3>
                    <p className="text-small text-default-500 mt-1">
                      Мы отправили 6-значный код на{" "}
                      <span className="font-medium">{email}</span>
                    </p>
                  </div>
                  <div className="flex justify-center">
                    <InputOtp
                      length={6}
                      value={code}
                      onValueChange={setCode}
                      variant="bordered"
                      color="success"
                      size="lg"
                      classNames={{
                        base: "gap-2",
                        segment:
                          "w-12 h-12 text-large font-semibold border-2 border-default-200 data-[focus-visible=true]:border-success-500 data-[focus-visible=true]:ring-2 data-[focus-visible=true]:ring-success-200 data-[filled=true]:border-success-300 data-[filled=true]:bg-success-50",
                      }}
                    />
                  </div>
                  <div className="text-center">
                    <Button
                      variant="light"
                      color="secondary"
                      size="sm"
                      onPress={() => {
                        setOtpSent(false);
                        setCode("");
                        setMessage("");
                      }}
                      className="text-small"
                    >
                      Не получили код? Попробуйте снова
                    </Button>
                  </div>
                </div>
                <Button
                  type="submit"
                  color="success"
                  variant="solid"
                  size="lg"
                  isLoading={isLoadingVerifyOtp}
                  isDisabled={code.length !== 6}
                  className="font-semibold"
                >
                  {isLoadingVerifyOtp ? "Проверка..." : "Проверить и войти"}
                </Button>
              </form>
            </>
          )}

          {/* Message Display */}
          {message && (
            <Chip
              color={
                messageType === "success"
                  ? "success"
                  : messageType === "error"
                    ? "danger"
                    : "primary"
              }
              variant="flat"
              className="w-full justify-center py-2"
            >
              {message}
            </Chip>
          )}
        </CardBody>
      </Card>
    </div>
  );
}

export default function LoginPage() {
  return (
    <HeroUIClientProvider>
      <LoginForm />
    </HeroUIClientProvider>
  );
}
