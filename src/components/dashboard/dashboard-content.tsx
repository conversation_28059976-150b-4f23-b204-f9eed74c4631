"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Divider,
  Progress,
} from "@heroui/react";
import { useState } from "react";

interface DashboardContentProps {
  userEmail: string;
}

export function DashboardContent({ userEmail }: DashboardContentProps) {
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    // Create a form and submit it
    const form = document.createElement("form");
    form.method = "POST";
    form.action = "/api/auth/signout";
    document.body.appendChild(form);
    form.submit();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-4">
            <Avatar
              size="lg"
              name={userEmail.charAt(0).toUpperCase()}
              className="bg-gradient-to-r from-pink-500 to-purple-600 text-white"
            />
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
                💕 Добро пожаловать в Lovebook
              </h1>
              <p className="text-default-500">Вошли как {userEmail}</p>
            </div>
          </div>
          <Button
            color="danger"
            variant="flat"
            onPress={handleSignOut}
            isLoading={isSigningOut}
            className="font-semibold"
          >
            {isSigningOut ? "Выход..." : "Выйти"}
          </Button>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Create New Book Card */}
          <Card className="col-span-1 md:col-span-2 lg:col-span-1">
            <CardHeader className="pb-4">
              <div className="flex flex-col">
                <h3 className="text-xl font-semibold text-foreground">
                  Создайте свою историю любви
                </h3>
                <p className="text-small text-default-500">
                  Начните создавать персонализированную книгу любви
                </p>
              </div>
            </CardHeader>
            <Divider />
            <CardBody className="gap-4">
              <div className="flex flex-col gap-3">
                <Button
                  color="secondary"
                  variant="solid"
                  size="lg"
                  className="bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold"
                >
                  ✨ Создать новую книгу
                </Button>
                <Button color="default" variant="bordered" size="md">
                  📖 Просмотреть шаблоны
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Recent Books Card */}
          <Card className="col-span-1 md:col-span-1 lg:col-span-1">
            <CardHeader className="pb-4">
              <div className="flex flex-col">
                <h3 className="text-xl font-semibold text-foreground">
                  Недавние книги
                </h3>
                <p className="text-small text-default-500">
                  Ваши последние творения
                </p>
              </div>
            </CardHeader>
            <Divider />
            <CardBody className="gap-3">
              <div className="flex flex-col gap-3">
                <div className="flex items-center justify-between p-3 bg-default-100 rounded-lg">
                  <div>
                    <p className="font-medium">Наш первый год</p>
                    <p className="text-small text-default-500">Черновик</p>
                  </div>
                  <Chip color="warning" variant="flat" size="sm">
                    В процессе
                  </Chip>
                </div>
                <div className="flex items-center justify-between p-3 bg-default-100 rounded-lg">
                  <div>
                    <p className="font-medium">Подарок на годовщину</p>
                    <p className="text-small text-default-500">Завершено</p>
                  </div>
                  <Chip color="success" variant="flat" size="sm">
                    Готово
                  </Chip>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Progress Card */}
          <Card className="col-span-1 md:col-span-1 lg:col-span-1">
            <CardHeader className="pb-4">
              <div className="flex flex-col">
                <h3 className="text-xl font-semibold text-foreground">
                  Ваш прогресс
                </h3>
                <p className="text-small text-default-500">
                  Продолжайте создавать прекрасные воспоминания
                </p>
              </div>
            </CardHeader>
            <Divider />
            <CardBody className="gap-4">
              <div className="flex flex-col gap-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-small">Создано книг</span>
                    <span className="text-small font-semibold">2/5</span>
                  </div>
                  <Progress
                    value={40}
                    color="secondary"
                    className="max-w-full"
                  />
                </div>
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-small">Написано страниц</span>
                    <span className="text-small font-semibold">24/50</span>
                  </div>
                  <Progress value={48} color="success" className="max-w-full" />
                </div>
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-small">Photos Added</span>
                    <span className="text-small font-semibold">18/30</span>
                  </div>
                  <Progress value={60} color="primary" className="max-w-full" />
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Quick Actions Card */}
          <Card className="col-span-1 md:col-span-2 lg:col-span-3">
            <CardHeader className="pb-4">
              <div className="flex flex-col">
                <h3 className="text-xl font-semibold text-foreground">
                  Быстрые действия
                </h3>
                <p className="text-small text-default-500">
                  Всё, что нужно для создания удивительных книг любви
                </p>
              </div>
            </CardHeader>
            <Divider />
            <CardBody>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button
                  variant="flat"
                  color="primary"
                  className="h-20 flex-col gap-2"
                >
                  <span className="text-2xl">📝</span>
                  <span className="text-small">Написать историю</span>
                </Button>
                <Button
                  variant="flat"
                  color="secondary"
                  className="h-20 flex-col gap-2"
                >
                  <span className="text-2xl">📸</span>
                  <span className="text-small">Добавить фото</span>
                </Button>
                <Button
                  variant="flat"
                  color="success"
                  className="h-20 flex-col gap-2"
                >
                  <span className="text-2xl">🎨</span>
                  <span className="text-small">Настроить</span>
                </Button>
                <Button
                  variant="flat"
                  color="warning"
                  className="h-20 flex-col gap-2"
                >
                  <span className="text-2xl">📤</span>
                  <span className="text-small">Поделиться</span>
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
}
