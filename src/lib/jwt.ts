import jwt from "jsonwebtoken";

export function signJwt(payload: object) {
  const expirationDays = Number.parseInt(
    process.env.JWT_EXPIRATION_DAYS || "7",
  );
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error("JWT_SECRET is not defined");
  }
  return jwt.sign(payload, secret, {
    expiresIn: `${expirationDays}d`,
  });
}

export function verifyJwt(token: string) {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error("JWT_SECRET is not defined");
  }
  return jwt.verify(token, secret);
}
